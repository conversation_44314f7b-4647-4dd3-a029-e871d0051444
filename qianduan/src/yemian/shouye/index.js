import React from 'react';
import styled from 'styled-components';
import { <PERSON><PERSON><PERSON>nqi } from '../../zujian/minganbuju/yangshihua<PERSON>jian.js';
import Lunboguanggao from './lunboguanggao.js';
import Gonggaolan from './gonggaolan.js';

// 首页主容器
const <PERSON><PERSON><PERSON><PERSON><PERSON> = styled(<PERSON><PERSON>nqi)`
  min-height: 100vh;
  padding: 80px 0 40px 0;
  display: flex;
  position: relative;

  @media (max-width: 768px) {
    flex-direction: column;
    padding: 0;
  }
`;

// 左侧广告区域
const Zuoceguanggaoquyu = styled.div`
  width: 480px;
  position: fixed;
  top: 80px;
  left: 10px;
  height: fit-content;
  max-height: calc(100vh - 100px);
  overflow: hidden;
  margin: 0;
  padding: 0;
  z-index: 50;

  @media (max-width: 768px) {
    width: 100%;
    position: static;
    max-height: none;
    margin: 0;
    padding: 60px 10px 10px 10px;
    left: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }
`;

// 主内容区域
const Zhu<PERSON><PERSON><PERSON>quy<PERSON> = styled.div`
  flex: 1;
  margin-left: 500px;
  padding: 0 20px;

  @media (max-width: 768px) {
    margin-left: 0;
    padding: 0 10px;
  }
`;

// 首页组件
function Shouye() {
  return (
    <Shouyerongqi>
      {/* 左侧广告区域 */}
      <Zuoceguanggaoquyu>
        <Lunboguanggao />
        <Gonggaolan />
      </Zuoceguanggaoquyu>

      {/* 主内容区域 */}
      <Zhuneirongquyu>
        {/* 这里可以放置主要内容 */}
      </Zhuneirongquyu>
    </Shouyerongqi>
  );
}

export default Shouye;
