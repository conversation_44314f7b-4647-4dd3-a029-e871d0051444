import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '../../zujian/minganbuju/yangshihuazujian.js';

// 公告栏容器
const Gong<PERSON><PERSON><PERSON><PERSON><PERSON> = styled(<PERSON><PERSON><PERSON><PERSON><PERSON>)`
  margin-top: 16px;
  padding: ${props => props.theme.jianju.zhongdeng};
  border-radius: 12px;
  position: relative;
  overflow: hidden;

  @media (max-width: 768px) {
    margin-top: 12px;
    padding: ${props => props.theme.jianju.xiao};
    border-radius: 10px;
  }
`;

// 公告标题
const Gonggaobiaoti = styled.h3`
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  font-size: ${props => props.theme.ziti.daxiao.da};
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  margin: 0 0 ${props => props.theme.jianju.zhongdeng} 0;
  text-align: center;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, 
      transparent 0%, 
      ${props => props.theme.yanse.zhuyao} 50%, 
      transparent 100%
    );
    border-radius: 1px;
  }

  @media (max-width: 768px) {
    font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
    margin-bottom: ${props => props.theme.jianju.xiao};
  }
`;

// 公告内容
const Gonggaoneirong = styled(Zhutiwenben)`
  line-height: ${props => props.theme.ziti.xinggao.kuansong};
  margin: ${props => props.theme.jianju.zhongdeng} 0 0 0;
  text-align: justify;
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};

  @media (max-width: 768px) {
    font-size: ${props => props.theme.ziti.daxiao.xiao};
    line-height: ${props => props.theme.ziti.xinggao.putong};
  }
`;

// QQ群信息容器
const Qqunxinxirongqi = styled.div`
  margin-top: ${props => props.theme.jianju.zhongdeng};
  padding: ${props => props.theme.jianju.xiao};
  background: ${props => props.theme.mingcheng === 'anhei'
    ? 'rgba(255, 255, 255, 0.03)'
    : 'rgba(0, 0, 0, 0.03)'};
  border-radius: ${props => props.theme.yuanjiao.xiao};
  border-left: 3px solid ${props => props.theme.yanse.zhuyao};
  
  @media (max-width: 768px) {
    margin-top: ${props => props.theme.jianju.xiao};
    padding: ${props => props.theme.jianju.xiaoxiao};
  }
`;

// QQ群号码
const Qqunhaoma = styled.span`
  color: ${props => props.theme.yanse.zhuyao};
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  font-size: ${props => props.theme.ziti.daxiao.da};
  
  @media (max-width: 768px) {
    font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  }
`;

// 动画配置
const donghuapeizhi = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: "easeOut" }
};

// 公告栏组件
function Gonggaolan() {
  return (
    <motion.div
      variants={donghuapeizhi}
      initial="initial"
      animate="animate"
    >
      <Gonggaolanrongqi>
        <Gonggaobiaoti>网站公告</Gonggaobiaoti>
        
        <Gonggaoneirong leixing="zhuyao">
          冒险岛小册子成立于2021年10月，是一个关于电脑端的网络游戏《冒险岛》（台服名称《新枫之谷》）的个人性综合攻略与数据库资料站，如果喜欢我们，请记住我们的网址 mxd.dvg.cn
        </Gonggaoneirong>
        
        <Gonggaoneirong leixing="ciyao">
          资料逐步完善中，如果您有好的意见或发现数据错误，可以加入我们的交流群提供资料。
        </Gonggaoneirong>
        
        <Qqunxinxirongqi>
          <Gonggaoneirong leixing="zhuyao" wubianju>
            冒险岛小册子QQ群：<Qqunhaoma>10086</Qqunhaoma>
          </Gonggaoneirong>
        </Qqunxinxirongqi>
      </Gonggaolanrongqi>
    </motion.div>
  );
}

export default Gonggaolan;
